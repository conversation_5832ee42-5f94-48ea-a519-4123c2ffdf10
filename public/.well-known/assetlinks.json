[{"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "de.mobilversichert.app.dev", "sha256_cert_fingerprints": ["33:D9:41:74:7C:7A:6B:9C:4E:1B:3F:EE:B5:1B:92:99:EC:7F:95:9B:40:31:9C:04:2C:5B:07:C8:1F:32:5F:E9"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "de.mobilversichert.app.dev", "sha256_cert_fingerprints": ["FA:C6:17:45:DC:09:03:78:6F:B9:ED:E6:2A:96:2B:39:9F:73:48:F0:BB:6F:89:9B:83:32:66:75:91:03:3B:9C"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "de.mobilversichert.app.staging", "sha256_cert_fingerprints": ["33:D9:41:74:7C:7A:6B:9C:4E:1B:3F:EE:B5:1B:92:99:EC:7F:95:9B:40:31:9C:04:2C:5B:07:C8:1F:32:5F:E9"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "de.mobilversichert.app.staging", "sha256_cert_fingerprints": ["FA:C6:17:45:DC:09:03:78:6F:B9:ED:E6:2A:96:2B:39:9F:73:48:F0:BB:6F:89:9B:83:32:66:75:91:03:3B:9C"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "de.mobilversichert.app", "sha256_cert_fingerprints": ["33:D9:41:74:7C:7A:6B:9C:4E:1B:3F:EE:B5:1B:92:99:EC:7F:95:9B:40:31:9C:04:2C:5B:07:C8:1F:32:5F:E9"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "de.mobilversichert.app", "sha256_cert_fingerprints": ["FA:C6:17:45:DC:09:03:78:6F:B9:ED:E6:2A:96:2B:39:9F:73:48:F0:BB:6F:89:9B:83:32:66:75:91:03:3B:9C"]}}]