import {
  DataManagementSearchParams,
  loadDataManagementSearchParams,
} from '@/modules/auth/libs/update-client-search-params'
import { DataManagementDynamicForm } from '@/modules/data-management/components/data-managment-dynamic-form'

export const dynamic = 'force-dynamic'

interface Props {
  searchParams: Promise<DataManagementSearchParams>
}

export default async function DataManagementPage({ searchParams }: Props) {
  const params = await loadDataManagementSearchParams(searchParams)

  return <DataManagementDynamicForm />
}
