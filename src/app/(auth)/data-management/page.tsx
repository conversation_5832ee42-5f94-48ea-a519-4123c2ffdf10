import { Suspense } from 'react'

import { loginWithToken } from '@/modules/auth/actions/auth-actions'
import { getSession } from '@/modules/auth/actions/session'
import {
  DataManagementSearchParams,
  loadDataManagementSearchParams,
} from '@/modules/auth/libs/update-client-search-params'
import { DataManagementAuthWrapper } from '@/modules/data-management/components/data-management-auth-wrapper'
import DataManagementFormView from '@/modules/data-management/views/data-managment-form-view'
import { redirect } from 'next/dist/server/api-utils'

export const dynamic = 'force-dynamic'

interface Props {
  searchParams: Promise<DataManagementSearchParams>
}

export default async function DataManagementPage({ searchParams }: Props) {
  const params = await loadDataManagementSearchParams(searchParams)
  const session = await getSession()

  if (!params.token) {
    redirect('/login')
  }

  return (
    <DataManagementAuthWrapper token={params.token} session={session}>
      <Suspense fallback={<div>Loading...</div>}>
        <DataManagementFormView />
      </Suspense>
    </DataManagementAuthWrapper>
  )
}
