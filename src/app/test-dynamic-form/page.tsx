'use client'

import React, { useState } from 'react'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { DynamicForm, FormConfig, FormData, FormStepperCallbacks } from '@/components/dynamic-form'
import multiStepConfig from '@/components/dynamic-form/examples/example-form.json'
import singleStepConfig from '@/components/dynamic-form/examples/single-step-form.json'

/**
 * Test page for both single-step and multi-step DynamicForm configurations
 */
export default function TestDynamicFormPage() {
  const [singleStepData, setSingleStepData] = useState<FormData | null>(null)
  const [multiStepData, setMultiStepData] = useState<FormData | null>(null)

  const singleStepCallbacks: FormStepperCallbacks = {
    onSubmit: async (data) => {
      setSingleStepData(data)
    },
  }

  const multiStepCallbacks: FormStepperCallbacks = {
    onSubmit: async (data) => {
      setMultiStepData(data)
    },
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Dynamic Form Test</h1>
        <p className="text-muted-foreground">
          Testing both single-step and multi-step form configurations to verify backward compatibility and new features.
        </p>
      </div>

      <Tabs defaultValue="single-step" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="single-step">Single-Step Form</TabsTrigger>
          <TabsTrigger value="multi-step">Multi-Step Form</TabsTrigger>
        </TabsList>

        <TabsContent value="single-step" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Single-Step Form Test</CardTitle>
              <CardDescription>
                Tests the DynamicForm component with a single step (should hide stepper navigation)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!singleStepData ? (
                <DynamicForm config={singleStepConfig as FormConfig} callbacks={singleStepCallbacks} />
              ) : (
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-2">Form Submitted Successfully!</h3>
                    <pre className="text-sm text-green-700 overflow-auto">
                      {JSON.stringify(singleStepData, null, 2)}
                    </pre>
                  </div>
                  <Button onClick={() => setSingleStepData(null)} variant="outline">
                    Reset Form
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="multi-step" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Multi-Step Form Test</CardTitle>
              <CardDescription>
                Tests the DynamicForm component with multiple steps (should show stepper navigation)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!multiStepData ? (
                <DynamicForm config={multiStepConfig as FormConfig} callbacks={multiStepCallbacks} />
              ) : (
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-2">Form Submitted Successfully!</h3>
                    <pre className="text-sm text-green-700 overflow-auto max-h-96">
                      {JSON.stringify(multiStepData, null, 2)}
                    </pre>
                  </div>
                  <Button onClick={() => setMultiStepData(null)} variant="outline">
                    Reset Form
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
