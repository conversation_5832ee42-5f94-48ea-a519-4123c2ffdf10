'use client'

import React, { useMemo } from 'react'

import { PlusIcon, Trash2Icon } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { FieldValues, useFieldArray } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import { FieldConfig, FieldRendererProps } from '../types'

// Component registry for dynamic array components
const COMPONENT_REGISTRY = {
  contracts: React.lazy(() =>
    import('./wrappers/contracts-wrapper').then((module) => ({
      default: module.ContractsWrapper,
    }))
  ),
  'payment-methods': React.lazy(() =>
    import('./wrappers/payment-wrapper').then((module) => ({
      default: module.PaymentWrapper,
    }))
  ),
} as const

type ComponentName = keyof typeof COMPONENT_REGISTRY

interface DynamicArrayFieldProps<TFieldValues extends FieldValues = FieldValues>
  extends FieldRendererProps<TFieldValues> {
  field: FieldConfig & {
    type: 'dynamic-array'
    componentName: string
    arrayConfig?: {
      minItems?: number
      maxItems?: number
      addButtonText?: string
      removeButtonText?: string
      emptyStateText?: string
      itemLabelField?: string
    }
  }
}

/**
 * DynamicArrayField component that handles arrays of complex objects
 *
 * This component uses React Hook Form's useFieldArray to manage arrays
 * and renders existing React components for each array item.
 */
export function DynamicArrayField<TFieldValues extends FieldValues = FieldValues>({
  field,
  form,
  disabled = false,
  className,
  sectionId,
}: DynamicArrayFieldProps<TFieldValues>) {
  const t = useTranslations()

  // Determine field name - use nested path if sectionId provided
  const fieldName = sectionId ? `${sectionId}.${field.name}` : field.name

  // Use useFieldArray for array management
  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: fieldName as any, // Type assertion needed for dynamic field paths
  })

  // Get the component to render
  const ComponentToRender = useMemo(() => {
    const componentName = field.componentName as ComponentName
    return COMPONENT_REGISTRY[componentName]
  }, [field.componentName])

  // Array configuration with defaults
  const arrayConfig = {
    minItems: 0,
    maxItems: undefined,
    addButtonText: 'Add Item',
    removeButtonText: 'Remove',
    emptyStateText: 'No items added yet',
    itemLabelField: 'name',
    ...field.arrayConfig,
  }

  // Handle adding new item
  const handleAddItem = () => {
    if (arrayConfig.maxItems && fields.length >= arrayConfig.maxItems) {
      return
    }

    // Add empty object - the component will handle initialization
    append({} as any)
  }

  // Handle removing item
  const handleRemoveItem = (index: number) => {
    if (fields.length <= arrayConfig.minItems) {
      return
    }

    remove(index)
  }

  // Handle updating item
  const handleUpdateItem = (index: number, data: any) => {
    update(index, data)
  }

  // Get item label for navigation
  const getItemLabel = (item: any, index: number) => {
    if (arrayConfig.itemLabelField && item[arrayConfig.itemLabelField]) {
      return item[arrayConfig.itemLabelField]
    }
    return `Item ${index + 1}`
  }

  // Check if we can add more items
  const canAddMore = !arrayConfig.maxItems || fields.length < arrayConfig.maxItems

  // Check if we can remove items
  const canRemove = fields.length > arrayConfig.minItems

  if (!ComponentToRender) {
    return (
      <div className={cn('p-4 border border-red-200 rounded-md bg-red-50', className)}>
        <p className="text-sm text-red-600">Component '{field.componentName}' not found in registry</p>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header with add button */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">{t(field.label)}</h3>
          {field.helperText && <p className="text-sm text-muted-foreground">{t(field.helperText)}</p>}
        </div>

        {canAddMore && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleAddItem}
            disabled={disabled}
            className="flex items-center gap-2"
          >
            <PlusIcon className="w-4 h-4" />
            {t(arrayConfig.addButtonText)}
          </Button>
        )}
      </div>

      {/* Array items */}
      {fields.length > 0 ? (
        <div className="space-y-4">
          {fields.map((item, index) => (
            <Card key={item.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">{getItemLabel(item, index)}</CardTitle>

                  {canRemove && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveItem(index)}
                      disabled={disabled}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2Icon className="w-4 h-4" />
                      <span className="sr-only">{t(arrayConfig.removeButtonText)}</span>
                    </Button>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                <React.Suspense fallback={<div>Loading...</div>}>
                  <ComponentToRender
                    {...(item as any)}
                    index={index}
                    onUpdate={(data: any) => handleUpdateItem(index, data)}
                    disabled={disabled}
                  />
                </React.Suspense>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        /* Empty state */
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-4">{t(arrayConfig.emptyStateText)}</p>

              {canAddMore && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddItem}
                  disabled={disabled}
                  className="flex items-center gap-2"
                >
                  <PlusIcon className="w-4 h-4" />
                  {t(arrayConfig.addButtonText)}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Item count info */}
      <div className="text-xs text-muted-foreground">
        {fields.length} item{fields.length !== 1 ? 's' : ''}
        {arrayConfig.maxItems && ` (max ${arrayConfig.maxItems})`}
        {arrayConfig.minItems > 0 && ` (min ${arrayConfig.minItems})`}
      </div>
    </div>
  )
}
