'use client'

import React from 'react'

import { ContractForm } from '@/modules/contracts/components/contract-form'
import { Product, Provider } from '@/modules/contracts/libs/contract-types'
import { ContractFormInputs } from '@/modules/contracts/types/contract-schema'
import { SelectOption } from '@/components/form-inputs'

interface ContractsWrapperProps {
  index: number
  onUpdate: (data: any) => void
  disabled?: boolean
  // Contract data
  id?: string
  isUpdated?: boolean
  // Additional props that might be passed from the parent
  providers?: Provider[]
  products?: Product[]
  categoryOptions?: SelectOption[]
  [key: string]: any
}

/**
 * Wrapper component for ContractForm to work with DynamicArrayField
 * 
 * This component adapts the existing ContractForm to work within
 * the dynamic array system by handling the onUpdate callback
 * and managing the contract data structure.
 */
export function ContractsWrapper({
  index,
  onUpdate,
  disabled = false,
  id,
  isUpdated = false,
  providers = [],
  products = [],
  categoryOptions = [],
  ...contractData
}: ContractsWrapperProps) {
  
  // Handle contract form completion
  const handleContractComplete = async (formData: ContractFormInputs) => {
    // Transform the form data to match the expected contract structure
    const updatedContract = {
      ...contractData,
      ...formData,
      id: id || `contract-${Date.now()}-${index}`,
      isUpdated: true,
    }
    
    // Call the onUpdate callback to update the array
    onUpdate(updatedContract)
  }

  // If this is a new contract (no id), create a minimal structure
  const contract = id ? {
    id,
    isUpdated,
    ...contractData,
  } : null

  return (
    <div className="space-y-4">
      {contract ? (
        <ContractForm
          className="max-w-none"
          onComplete={handleContractComplete}
          editForm={true}
          contract={contract}
          categoryOptions={categoryOptions}
          providers={providers}
          products={products}
          isLoading={disabled}
        />
      ) : (
        <div className="p-4 border border-dashed border-gray-300 rounded-lg">
          <p className="text-sm text-gray-500 text-center">
            New contract - click to configure
          </p>
        </div>
      )}
    </div>
  )
}
