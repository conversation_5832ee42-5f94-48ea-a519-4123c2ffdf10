'use client'

import React, { useState } from 'react'

import { useTranslations } from 'next-intl'
import { CreditCard } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import { PaymentMethod, PaymentMethodType } from '@/modules/data-management/types/payment-schema'
import { CreditCardForm } from '@/modules/data-management/components/steps/payment/credit-card-form'
import { SEPAForm } from '@/modules/data-management/components/steps/payment/sepa-form'
import { PaymentMethodCard } from '@/modules/data-management/components/steps/payment/payment-method-card'

interface PaymentWrapperProps {
  index: number
  onUpdate: (data: any) => void
  disabled?: boolean
  // Payment method data
  id?: string
  type?: PaymentMethodType
  // Additional props
  userProfile?: any
  billingAddress?: any
  [key: string]: any
}

/**
 * Wrapper component for payment methods to work with DynamicArrayField
 * 
 * This component adapts the existing payment forms to work within
 * the dynamic array system by handling different payment method types
 * and managing the payment data structure.
 */
export function PaymentWrapper({
  index,
  onUpdate,
  disabled = false,
  id,
  type,
  userProfile,
  billingAddress,
  ...paymentData
}: PaymentWrapperProps) {
  const t = useTranslations()
  
  const [isEditing, setIsEditing] = useState(!id) // Edit mode if no id (new payment method)
  const [selectedType, setSelectedType] = useState<PaymentMethodType>(type || 'creditCard')

  // Handle payment method save
  const handleSavePaymentMethod = (method: PaymentMethod) => {
    const updatedMethod = {
      ...method,
      id: id || `payment-${Date.now()}-${index}`,
    }
    
    onUpdate(updatedMethod)
    setIsEditing(false)
  }

  // Handle cancel
  const handleCancel = () => {
    if (!id) {
      // If this is a new payment method, remove it
      onUpdate(null)
    } else {
      setIsEditing(false)
    }
  }

  // Handle edit
  const handleEdit = () => {
    setIsEditing(true)
  }

  // Handle delete
  const handleDelete = () => {
    onUpdate(null)
  }

  // Render payment method form based on type
  const renderPaymentMethodForm = () => {
    switch (selectedType) {
      case 'creditCard':
        return (
          <CreditCardForm
            userProfile={userProfile}
            billingAddress={billingAddress || {}}
            onSubmit={handleSavePaymentMethod}
            onCancel={handleCancel}
            initialData={type === 'creditCard' ? { id, type, ...paymentData } : undefined}
          />
        )
      case 'sepa':
        return (
          <SEPAForm
            onSubmit={handleSavePaymentMethod}
            onCancel={handleCancel}
            initialData={type === 'sepa' ? { id, type, ...paymentData } : undefined}
          />
        )
      case 'cash':
        return (
          <Card>
            <CardHeader>
              <CardTitle>{t('PAYMENT.EDIT.CASH.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-3 justify-end">
                <Button type="button" variant="outline" onClick={handleCancel}>
                  {t('PAYMENT.EDIT.ACTIONS.CANCEL')}
                </Button>
                <Button
                  onClick={() =>
                    handleSavePaymentMethod({
                      id: id || `cash-${Date.now()}-${index}`,
                      type: 'cash',
                      notes: 'Cash payment method',
                    })
                  }
                >
                  {t('PAYMENT.EDIT.ACTIONS.SAVE')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )
      case 'cheque':
        return (
          <Card>
            <CardHeader>
              <CardTitle>{t('PAYMENT.EDIT.CHEQUE.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-3 justify-end">
                <Button type="button" variant="outline" onClick={handleCancel}>
                  {t('PAYMENT.EDIT.ACTIONS.CANCEL')}
                </Button>
                <Button
                  onClick={() =>
                    handleSavePaymentMethod({
                      id: id || `cheque-${Date.now()}-${index}`,
                      type: 'cheque',
                      notes: 'Cheque payment method',
                    })
                  }
                >
                  {t('PAYMENT.EDIT.ACTIONS.SAVE')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )
      default:
        return null
    }
  }

  // If editing or new payment method, show form
  if (isEditing) {
    return (
      <div className="space-y-4">
        {/* Payment type selection for new methods */}
        {!id && (
          <div>
            <h4 className="text-sm font-medium mb-2">{t('PAYMENT.EDIT.SELECT_PAYMENT_TYPE')}</h4>
            <div className="flex gap-2 flex-wrap">
              {(['creditCard', 'sepa', 'cash', 'cheque'] as PaymentMethodType[]).map((paymentType) => (
                <Button
                  key={paymentType}
                  type="button"
                  variant={selectedType === paymentType ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedType(paymentType)}
                >
                  {t(`PAYMENT.TYPE.${paymentType.toUpperCase()}`)}
                </Button>
              ))}
            </div>
          </div>
        )}
        
        {/* Payment method form */}
        {renderPaymentMethodForm()}
      </div>
    )
  }

  // If we have a payment method, show the card
  if (id && type) {
    const paymentMethod: PaymentMethod = {
      id,
      type,
      ...paymentData,
    }

    return (
      <div className="space-y-2">
        <PaymentMethodCard
          paymentMethod={paymentMethod}
          active={false}
          onSelect={() => {}} // Not needed in this context
          onDelete={handleDelete}
          onEdit={handleEdit}
        />
      </div>
    )
  }

  // Empty state for new payment method
  return (
    <Card className="border-dashed">
      <CardContent className="py-6">
        <div className="text-center">
          <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
            <CreditCard className="w-5 h-5 text-gray-400" />
          </div>
          <p className="text-sm text-gray-500 mb-3">New payment method</p>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            disabled={disabled}
          >
            Configure Payment Method
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
