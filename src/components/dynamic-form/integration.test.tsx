import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { DynamicForm } from './components/dynamic-form'
import { FormConfig } from './types'

// Mock the wrapper components
jest.mock('./components/wrappers/contracts-wrapper', () => ({
  ContractsWrapper: ({ index, onUpdate }: any) => (
    <div data-testid={`contract-wrapper-${index}`}>
      <input
        data-testid={`contract-input-${index}`}
        onChange={(e) => onUpdate({ id: `contract-${index}`, name: e.target.value })}
        placeholder="Contract name"
      />
    </div>
  ),
}))

jest.mock('./components/wrappers/payment-wrapper', () => ({
  PaymentWrapper: ({ index, onUpdate }: any) => (
    <div data-testid={`payment-wrapper-${index}`}>
      <select
        data-testid={`payment-select-${index}`}
        onChange={(e) => onUpdate({ id: `payment-${index}`, type: e.target.value })}
      >
        <option value="">Select payment type</option>
        <option value="creditCard">Credit Card</option>
        <option value="sepa">SEPA</option>
      </select>
    </div>
  ),
}))

describe('DynamicForm Integration with Dynamic Arrays', () => {
  const testConfig: FormConfig = {
    title: 'Test Form',
    steps: [
      {
        id: 'basic',
        title: 'Basic Information',
        fields: [
          {
            name: 'name',
            type: 'input',
            label: 'Name',
            isRequired: true,
          },
          {
            name: 'email',
            type: 'input',
            label: 'Email',
            isRequired: true,
            validation: [
              { type: 'email', message: 'Invalid email' },
            ],
          },
        ],
      },
      {
        id: 'contracts',
        title: 'Contracts',
        fields: [
          {
            name: 'contractsList',
            type: 'dynamic-array',
            label: 'Contracts',
            componentName: 'contracts',
            arrayConfig: {
              minItems: 0,
              maxItems: 3,
              addButtonText: 'Add Contract',
              removeButtonText: 'Remove Contract',
              emptyStateText: 'No contracts added',
              itemLabelField: 'name',
            },
          },
        ],
      },
      {
        id: 'payment',
        title: 'Payment Methods',
        fields: [
          {
            name: 'paymentMethods',
            type: 'dynamic-array',
            label: 'Payment Methods',
            componentName: 'payment-methods',
            isRequired: true,
            arrayConfig: {
              minItems: 1,
              maxItems: 2,
              addButtonText: 'Add Payment Method',
              removeButtonText: 'Remove Payment Method',
              emptyStateText: 'No payment methods added',
              itemLabelField: 'type',
            },
            validation: [
              { type: 'required', message: 'At least one payment method required' },
            ],
          },
        ],
      },
    ],
  }

  const mockCallbacks = {
    onSubmit: jest.fn(),
    onStepChange: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders all steps with dynamic array fields', () => {
    render(<DynamicForm config={testConfig} callbacks={mockCallbacks} />)
    
    // Should show first step initially
    expect(screen.getByText('Basic Information')).toBeInTheDocument()
    expect(screen.getByLabelText('Name')).toBeInTheDocument()
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
  })

  it('navigates through steps and shows dynamic arrays', async () => {
    render(<DynamicForm config={testConfig} callbacks={mockCallbacks} />)
    
    // Fill basic information
    fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    
    // Go to next step
    fireEvent.click(screen.getByText('Next'))
    
    await waitFor(() => {
      expect(screen.getByText('Contracts')).toBeInTheDocument()
      expect(screen.getByText('No contracts added')).toBeInTheDocument()
      expect(screen.getByText('Add Contract')).toBeInTheDocument()
    })
  })

  it('handles dynamic array operations correctly', async () => {
    render(<DynamicForm config={testConfig} callbacks={mockCallbacks} />)
    
    // Navigate to contracts step
    fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    fireEvent.click(screen.getByText('Next'))
    
    await waitFor(() => {
      expect(screen.getByText('Add Contract')).toBeInTheDocument()
    })
    
    // Add a contract
    fireEvent.click(screen.getByText('Add Contract'))
    
    await waitFor(() => {
      expect(screen.getByTestId('contract-wrapper-0')).toBeInTheDocument()
    })
    
    // Add contract data
    fireEvent.change(screen.getByTestId('contract-input-0'), { 
      target: { value: 'Test Contract' } 
    })
  })

  it('validates required dynamic arrays', async () => {
    render(<DynamicForm config={testConfig} callbacks={mockCallbacks} />)
    
    // Navigate to payment step
    fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    fireEvent.click(screen.getByText('Next'))
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('Next'))
    })
    
    await waitFor(() => {
      expect(screen.getByText('Payment Methods')).toBeInTheDocument()
      expect(screen.getByText('No payment methods added')).toBeInTheDocument()
    })
    
    // Try to submit without adding payment methods (should fail validation)
    fireEvent.click(screen.getByText('Submit'))
    
    // Should show validation error or prevent submission
    expect(mockCallbacks.onSubmit).not.toHaveBeenCalled()
  })

  it('respects array limits', async () => {
    render(<DynamicForm config={testConfig} callbacks={mockCallbacks} />)
    
    // Navigate to contracts step
    fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    fireEvent.click(screen.getByText('Next'))
    
    await waitFor(() => {
      expect(screen.getByText('Add Contract')).toBeInTheDocument()
    })
    
    // Add maximum contracts (3)
    fireEvent.click(screen.getByText('Add Contract'))
    fireEvent.click(screen.getByText('Add Contract'))
    fireEvent.click(screen.getByText('Add Contract'))
    
    await waitFor(() => {
      expect(screen.getByTestId('contract-wrapper-0')).toBeInTheDocument()
      expect(screen.getByTestId('contract-wrapper-1')).toBeInTheDocument()
      expect(screen.getByTestId('contract-wrapper-2')).toBeInTheDocument()
    })
    
    // Add button should be disabled/hidden when max reached
    expect(screen.queryByText('Add Contract')).not.toBeInTheDocument()
  })

  it('submits form with dynamic array data', async () => {
    render(<DynamicForm config={testConfig} callbacks={mockCallbacks} />)
    
    // Fill all required fields and navigate through steps
    fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    fireEvent.click(screen.getByText('Next'))
    
    // Skip contracts (optional)
    await waitFor(() => {
      fireEvent.click(screen.getByText('Next'))
    })
    
    // Add required payment method
    await waitFor(() => {
      fireEvent.click(screen.getByText('Add Payment Method'))
    })
    
    await waitFor(() => {
      fireEvent.change(screen.getByTestId('payment-select-0'), { 
        target: { value: 'creditCard' } 
      })
    })
    
    // Submit form
    fireEvent.click(screen.getByText('Submit'))
    
    await waitFor(() => {
      expect(mockCallbacks.onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          basic: expect.objectContaining({
            name: 'John Doe',
            email: '<EMAIL>',
          }),
          contracts: expect.objectContaining({
            contractsList: [],
          }),
          payment: expect.objectContaining({
            paymentMethods: expect.arrayContaining([
              expect.objectContaining({
                id: 'payment-0',
                type: 'creditCard',
              }),
            ]),
          }),
        })
      )
    })
  })
})
