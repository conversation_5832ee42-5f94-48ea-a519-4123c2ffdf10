import { z } from 'zod'

import { getFieldDefaultValue } from '../components/field-renderer'
import { FieldConfig, ValidationRule } from '../types'

/**
 * Dynamic Zod schema generator for form stepper fields
 *
 * This utility generates Zod validation schemas based on JSON field configurations
 * and validation rules, following the patterns used in existing modules like
 * contracts and damages.
 */

/**
 * Generate a Zod schema for a single field based on its configuration
 */
export function generateFieldSchema(field: FieldConfig): z.ZodTypeAny {
  let schema: z.ZodTypeAny

  // Start with base schema based on field type
  switch (field.type) {
    case 'input':
    case 'textarea':
      schema = z.string()
      break

    case 'select':
    case 'country':
      schema = z.string()
      break

    case 'multiselect':
      schema = z.array(z.string()).default([])
      break

    case 'checkbox':
    case 'switch':
      schema = z.boolean()
      break

    case 'date':
    case 'datetime':
    case 'month':
      // Accept both Date objects and valid date strings (for database serialization)
      schema = z
        .union([
          z.date(),
          z.string().transform((str, ctx) => {
            // Handle empty strings
            if (!str || str.trim() === '') {
              return undefined
            }

            const date = new Date(str)
            if (isNaN(date.getTime())) {
              ctx.addIssue({
                code: z.ZodIssueCode.invalid_date,
                message: 'FORM_STEPPER.VALIDATION.DATE_INVALID',
              })
              return z.NEVER
            }
            return date
          }),
        ])
        .transform((value) => {
          // Ensure we always return a Date object or undefined
          if (value === undefined) return undefined
          return value instanceof Date ? value : new Date(value)
        })
      break

    case 'phone':
      schema = z.string()
      break

    case 'upload':
      schema = z.array(z.any()).optional()
      break

    case 'dynamic-array':
      // For dynamic arrays, create an array schema that accepts any object structure
      // The actual validation will be handled by the embedded components
      schema = z.array(z.record(z.any())).default([])
      break

    default:
      schema = z.string()
  }

  // Apply validation rules
  if (field.validation) {
    schema = applyValidationRules(schema, field.validation, field.type)
  }

  // Handle required fields - check both validation array and isRequired property
  const requiredValidationRule = field.validation?.find((rule) => rule.type === 'required')
  const isFieldRequired = field.isRequired || !!requiredValidationRule

  if (isFieldRequired) {
    // Use custom message from validation rule if available, otherwise use default
    const requiredMessage = requiredValidationRule?.message || 'FORM_STEPPER.VALIDATION.REQUIRED'

    if (field.type === 'checkbox' || field.type === 'switch') {
      // For checkboxes, required means it must be true
      schema = schema.refine((val) => val === true, {
        message: requiredMessage,
      })
    } else if (field.type === 'multiselect') {
      // For multiselect, required means at least one item selected
      schema = (schema as z.ZodArray<z.ZodString>).min(1, requiredMessage)
    } else if (field.type === 'date' || field.type === 'datetime' || field.type === 'month') {
      // For date fields, required means the date must be provided
      schema = schema.refine((val) => val !== undefined && val !== null, {
        message: requiredMessage,
      })
    } else if (field.type === 'upload') {
      // For upload fields, required means at least one file
      schema = (schema as z.ZodArray<z.ZodAny>).min(1, requiredMessage)
    } else if (field.type === 'dynamic-array') {
      // For dynamic arrays, required means at least one item
      const minItems = field.arrayConfig?.minItems || 1
      schema = (schema as z.ZodArray<z.ZodTypeAny>).min(minItems, requiredMessage)
    } else {
      // For string-based fields, add required validation
      schema = (schema as z.ZodString).min(1, requiredMessage)
    }
  } else {
    // Make field optional if not required
    schema = schema.optional()
  }

  return schema
}

/**
 * Apply validation rules to a Zod schema
 */
function applyValidationRules(schema: z.ZodTypeAny, rules: ValidationRule[], fieldType: string): z.ZodTypeAny {
  let updatedSchema = schema

  for (const rule of rules) {
    updatedSchema = applyValidationRule(updatedSchema, rule, fieldType)
  }

  return updatedSchema
}

/**
 * Apply a single validation rule to a Zod schema
 */
function applyValidationRule(schema: z.ZodTypeAny, rule: ValidationRule, fieldType: string): z.ZodTypeAny {
  switch (rule.type) {
    case 'required':
      // Required is handled separately in generateFieldSchema
      return schema

    case 'minLength':
      if (typeof rule.value === 'number') {
        if (fieldType === 'multiselect' || fieldType === 'dynamic-array') {
          return (schema as z.ZodArray<z.ZodTypeAny>).min(rule.value, rule.message)
        } else {
          return (schema as z.ZodString).min(rule.value, rule.message)
        }
      }
      return schema

    case 'maxLength':
      if (typeof rule.value === 'number') {
        if (fieldType === 'multiselect' || fieldType === 'dynamic-array') {
          return (schema as z.ZodArray<z.ZodTypeAny>).max(rule.value, rule.message)
        } else {
          return (schema as z.ZodString).max(rule.value, rule.message)
        }
      }
      return schema

    case 'min':
      if (typeof rule.value === 'number') {
        return (schema as z.ZodNumber).min(rule.value, rule.message)
      }
      return schema

    case 'max':
      if (typeof rule.value === 'number') {
        return (schema as z.ZodNumber).max(rule.value, rule.message)
      }
      return schema

    case 'email':
      return (schema as z.ZodString).email(rule.message)

    case 'regex':
      if (rule.pattern) {
        const regex = new RegExp(rule.pattern)
        return (schema as z.ZodString).regex(regex, rule.message)
      }
      return schema

    case 'isTrue':
      return schema.refine((val) => val === true, {
        message: rule.message,
      })

    case 'custom':
      // For custom validation, we'll need to implement specific logic
      // This could be extended based on specific requirements
      console.warn('Custom validation rules are not yet implemented')
      return schema

    default:
      console.warn(`Unknown validation rule type: ${rule.type}`)
      return schema
  }
}

/**
 * Generate a complete Zod schema for a step's fields
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function generateStepSchema(fields: FieldConfig[]): z.ZodObject<any> {
  const schemaShape: Record<string, z.ZodTypeAny> = {}

  for (const field of fields) {
    schemaShape[field.name] = generateFieldSchema(field)
  }

  return z.object(schemaShape)
}

/**
 * Generate a complete Zod schema for all form steps (legacy flat structure)
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function generateFormSchema(allFields: FieldConfig[]): z.ZodObject<any> {
  const schemaShape: Record<string, z.ZodTypeAny> = {}

  for (const field of allFields) {
    schemaShape[field.name] = generateFieldSchema(field)
  }

  return z.object(schemaShape)
}

/**
 * Generate a nested Zod schema organized by logical sections
 * Each section contains its fields plus a lastUpdate field with current date
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function generateNestedFormSchema(steps: Array<{ id: string; fields: FieldConfig[] }>): z.ZodObject<any> {
  const sectionSchemas: Record<string, Record<string, z.ZodTypeAny>> = {}

  // Group fields by section
  for (const step of steps) {
    const sectionName = step.id

    if (!sectionSchemas[sectionName]) {
      sectionSchemas[sectionName] = {}
    }

    // Add all fields from this step to the section
    for (const field of step.fields) {
      sectionSchemas[sectionName][field.name] = generateFieldSchema(field)
    }
  }

  // Create the final nested schema
  const nestedSchemaShape: Record<string, z.ZodTypeAny> = {}

  for (const [sectionName, sectionFields] of Object.entries(sectionSchemas)) {
    // Add lastUpdate field to each section
    const sectionShape = {
      ...sectionFields,
      lastUpdate: z.string().default(() => new Date().toISOString().split('T')[0]), // YYYY-MM-DD format
    }

    nestedSchemaShape[sectionName] = z.object(sectionShape)
  }

  return z.object(nestedSchemaShape)
}

/**
 * Generate nested default values organized by sections
 */

export function generateNestedDefaultValues(
  steps: Array<{ id: string; fields: FieldConfig[] }>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  initialData: Record<string, any> = {}
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Record<string, any> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const sectionDefaults: Record<string, Record<string, any>> = {}

  // Group fields by section and generate defaults
  for (const step of steps) {
    const sectionName = step.id

    if (!sectionDefaults[sectionName]) {
      sectionDefaults[sectionName] = {
        lastUpdate: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
      }
    }

    // Add default values for all fields in this step
    for (const field of step.fields) {
      // Check if we have initial data for this field (could be in nested or flat format)
      let initialValue = initialData[field.name]
      if (initialValue === undefined && initialData[sectionName]) {
        initialValue = initialData[sectionName][field.name]
      }

      sectionDefaults[sectionName][field.name] = initialValue ?? getFieldDefaultValue(field)
    }
  }

  return sectionDefaults
}

/**
 * Transform flat form data to nested structure
 */

export function transformFlatToNested(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  flatData: Record<string, any>,
  steps: Array<{ id: string; fields: FieldConfig[] }>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Record<string, any> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const nestedData: Record<string, Record<string, any>> = {}

  // Group fields by section
  for (const step of steps) {
    const sectionName = step.id

    if (!nestedData[sectionName]) {
      nestedData[sectionName] = {
        lastUpdate: new Date().toISOString().split('T')[0],
      }
    }

    // Move fields from flat structure to nested structure
    for (const field of step.fields) {
      if (flatData[field.name] !== undefined) {
        nestedData[sectionName][field.name] = flatData[field.name]
      }
    }
  }

  return nestedData
}

/**
 * Transform nested form data to flat structure (for backward compatibility)
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function transformNestedToFlat(nestedData: Record<string, any>): Record<string, any> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const flatData: Record<string, any> = {}

  for (const [, sectionData] of Object.entries(nestedData)) {
    if (typeof sectionData === 'object' && sectionData !== null) {
      for (const [fieldName, fieldValue] of Object.entries(sectionData)) {
        // Skip the lastUpdate field when flattening
        if (fieldName !== 'lastUpdate') {
          flatData[fieldName] = fieldValue
        }
      }
    }
  }

  return flatData
}

/**
 * Validate form data against generated schema
 */

export async function validateFormData(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: Record<string, any>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  schema: z.ZodObject<any>
): Promise<{
  success: boolean
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any
  errors?: Record<string, string[]>
}> {
  try {
    const validatedData = await schema.parseAsync(data)
    return {
      success: true,
      data: validatedData,
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string[]> = {}

      for (const issue of error.issues) {
        const fieldName = issue.path.join('.')
        if (!errors[fieldName]) {
          errors[fieldName] = []
        }
        errors[fieldName].push(issue.message)
      }

      return {
        success: false,
        errors,
      }
    }

    return {
      success: false,
      errors: {
        _form: ['An unexpected validation error occurred'],
      },
    }
  }
}

/**
 * Get validation schema for specific field types
 */
export function getFieldTypeSchema(fieldType: string): z.ZodTypeAny {
  switch (fieldType) {
    case 'input':
    case 'textarea':
    case 'select':
    case 'country':
    case 'phone':
      return z.string()

    case 'multiselect':
      return z.array(z.string())

    case 'checkbox':
    case 'switch':
      return z.boolean()

    case 'date':
    case 'datetime':
    case 'month':
      // Accept both Date objects and valid date strings (consistent with generateFieldSchema)
      return z
        .union([
          z.date(),
          z.string().transform((str) => {
            if (!str || str.trim() === '') return undefined
            const date = new Date(str)
            return isNaN(date.getTime()) ? undefined : date
          }),
        ])
        .transform((value) => {
          if (value === undefined) return undefined
          return value instanceof Date ? value : new Date(value)
        })

    case 'upload':
      return z.array(z.any())

    case 'dynamic-array':
      return z.array(z.record(z.any()))

    default:
      return z.string()
  }
}

/**
 * Create validation rule helpers for common patterns
 */
export const validationRuleHelpers = {
  required: (message = 'this field is required'): ValidationRule => ({
    type: 'required',
    message,
  }),

  minLength: (length: number, message = 'FORM_STEPPER.VALIDATION.MIN_LENGTH'): ValidationRule => ({
    type: 'minLength',
    value: length,
    message,
  }),

  maxLength: (length: number, message = 'FORM_STEPPER.VALIDATION.MAX_LENGTH'): ValidationRule => ({
    type: 'maxLength',
    value: length,
    message,
  }),

  email: (message = 'FORM_STEPPER.VALIDATION.EMAIL'): ValidationRule => ({
    type: 'email',
    message,
  }),

  min: (value: number, message = 'FORM_STEPPER.VALIDATION.MIN'): ValidationRule => ({
    type: 'min',
    value,
    message,
  }),

  max: (value: number, message = 'FORM_STEPPER.VALIDATION.MAX'): ValidationRule => ({
    type: 'max',
    value,
    message,
  }),

  regex: (pattern: string, message = 'FORM_STEPPER.VALIDATION.PATTERN'): ValidationRule => ({
    type: 'regex',
    pattern,
    message,
  }),

  isTrue: (message = 'FORM_STEPPER.VALIDATION.MUST_BE_TRUE'): ValidationRule => ({
    type: 'isTrue',
    message,
  }),
}

/**
 * Transform form data for submission
 * Handles type conversions and formatting
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function transformFormData(data: Record<string, any>, fields: FieldConfig[]): Record<string, any> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const transformed: Record<string, any> = {}

  for (const field of fields) {
    const value = data[field.name]

    if (value === undefined || value === null) {
      transformed[field.name] = value
      continue
    }

    switch (field.type) {
      case 'date':
      case 'datetime':
      case 'month':
        // Ensure dates are properly formatted
        if (value instanceof Date) {
          transformed[field.name] = value
        } else if (typeof value === 'string') {
          transformed[field.name] = new Date(value)
        } else {
          transformed[field.name] = value
        }
        break

      case 'multiselect':
        // Ensure multiselect values are arrays
        transformed[field.name] = Array.isArray(value) ? value : [value].filter(Boolean)
        break

      case 'checkbox':
      case 'switch':
        // Ensure boolean values
        transformed[field.name] = Boolean(value)
        break

      case 'dynamic-array':
        // Ensure dynamic array values are arrays
        transformed[field.name] = Array.isArray(value) ? value : []
        break

      default:
        transformed[field.name] = value
    }
  }

  return transformed
}
