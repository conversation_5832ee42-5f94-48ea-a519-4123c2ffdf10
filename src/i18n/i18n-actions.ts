'use server'

import { getCookie, setCookie } from 'cookies-next/server'
import { cookies } from 'next/headers'

import { fetchApi } from '@/lib/fetch-api'

import { defaultLocale, transformTranslations, USER_LOCALE_COOKIE_NAME } from './i18n-libs'

export const getUserLocale = async () => {
  const userLocale = await getCookie(USER_LOCALE_COOKIE_NAME, { cookies })

  return userLocale || defaultLocale
}

export const setUserLocale = async (locale: string) => {
  await setCookie(USER_LOCALE_COOKIE_NAME, locale, { cookies })
}

export const fetchTranslations = async (locale: string) => {
  const params = {
    language: locale,
    collection: 'B2C_FE',
  }

  const paramsString = `"language":"${params.language}","collection":"${params.collection}"`

  const result = await fetchApi(`/language?params=${encodeURIComponent(paramsString)}`, {
    cache: 'force-cache',
    next: {
      // 1h
      revalidate: 60 * 60,
    },
  })

  if (result.error) {
    return null
  }
  const messages = result?.data?.data || {}

  return transformTranslations(messages)
}
