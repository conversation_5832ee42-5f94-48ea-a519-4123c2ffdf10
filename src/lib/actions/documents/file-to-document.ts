// Schema

import { DocumentInputs, DocumentOwnerType, DocumentUploadType } from '@/lib/actions/documents/documents-types'

// Server Actions

export async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = (error) => reject(error)
  })
}

export async function fileToDocument(
  file: File,
  ownerType: DocumentOwnerType = 'CLIENT',
  documentType?: DocumentUploadType,
  ownerId: string = '1'
) {
  const base64Data = await fileToBase64(file)
  let checkType = documentType
  if (!checkType) {
    const isImage = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)
    checkType = isImage ? 'PICTURE' : 'DOCUMENT'
  }

  const documentData: DocumentInputs = {
    file: {
      type: file.type,
      name: file.name,
    },
    name: file.name,
    image: base64Data,
    type: checkType,
    owner_type: ownerType,
    owner_id: ownerId,
  }

  return documentData

  // return await uploadDocumentBase64Action(documentData)
}
