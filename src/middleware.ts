import { NextRequest, NextResponse } from 'next/server'

import { getSessionCookie } from './modules/auth/actions/session'

const publicRoutes = ['/login', '/sign-up', '/welcome', '/forgot-password', '/data-management']
const apiRoutes = ['/api/auth/refresh-session', '/api/auth/session', '/api/auth/refresh']

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  const isPublicRoute = publicRoutes.includes(pathname)

  const encryptedSession = await getSessionCookie()

  if (isPublicRoute || apiRoutes.some((route) => pathname.startsWith(route))) {
    return NextResponse.next()
  }

  if (!encryptedSession) {
    console.log('🔒 [Middleware] No session cookie found, redirecting to login...')
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // Case 3: Valid session cookie exists - allow access (let components handle expiration)
  return NextResponse.next()
}

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}
