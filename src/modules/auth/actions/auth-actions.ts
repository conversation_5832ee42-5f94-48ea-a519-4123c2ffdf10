'use server'

import { getAppConfig } from '@/app-config'
import { deleteCookie, setCookie } from 'cookies-next/server'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

import { encryptSession } from '@/lib/encrypt-session'
import { createDetailedError, fetchApi } from '@/lib/fetch-api'
import { action } from '@/lib/safe-actions'

import { forgotPasswordSchema, loginSchema, loginWithTokenSchema, signupSchema } from '../types/auth-schema'
import { AuthSession, LoginResponse, UserProfileResponse } from '../types/auth-types'
import { DATA_MANAGEMENT_TOKEN_COOKIE_NAME, SESSION_COOKIE_NAME } from './constants'

export const loginUser = action.schema(loginSchema).action(async ({ parsedInput: { username, password } }) => {
  const config = getAppConfig()
  if (!config.ssoUrl) {
    throw new Error('SSO URL is not defined')
  }

  const loginResponse = await fetchApi<LoginResponse>(`${config.ssoUrl}/oauth`, {
    method: 'POST',
    body: JSON.stringify({
      grant_type: 'password',
      scope: 'client',
      username,
      password,
    }),
  })

  if (loginResponse.error || !loginResponse.data) {
    throw createDetailedError(loginResponse.error || 'Login failed', loginResponse.details)
  }

  const userProfileResponse = await fetchApi<UserProfileResponse>(`/profile`, {
    newApi: true,
    headers: {
      Authorization: `Bearer ${loginResponse.data?.access_token}`,
    },
  })

  if (userProfileResponse.error || !userProfileResponse.data) {
    throw createDetailedError(userProfileResponse.error || 'Failed to fetch user profile', userProfileResponse.details)
  }

  const user = userProfileResponse.data?.profile

  if (!user) {
    throw new Error('User profile not found')
  }

  const session: AuthSession = {
    accessToken: loginResponse.data.access_token,
    refreshToken: loginResponse.data.refresh_token,
    // Use actual expiration from server
    expiresAt: Date.now() + loginResponse.data.expires_in * 1000,
    expiresIn: loginResponse.data.expires_in,
    user: {
      id: user.id,
      email: user.email,
      agentId: user.agentId,
      agencyId: user.agencyId,
    },
  }

  const encryptedSession = await encryptSession(session)

  await setCookie(SESSION_COOKIE_NAME, encryptedSession, { cookies })

  return session
})

export const loginWithToken = action.schema(loginWithTokenSchema).action(async ({ parsedInput: { token } }) => {
  // const loginResponse = await fetchApi('/login_by_token', {
  //   newApi: true,
  //   method: 'POST',
  //   body: JSON.stringify({ tokenValue: token }),
  // })

  const loginResponse = await loginUser({
    username: '<EMAIL>',
    password: 'Test123!',
  })

  if (loginResponse?.serverError) {
    throw createDetailedError(loginResponse.serverError)
  }

  await setCookie(DATA_MANAGEMENT_TOKEN_COOKIE_NAME, token, { cookies })

  return {
    data: loginResponse?.data,
  }
})

export const logout = action.action(async () => {
  await deleteCookie(SESSION_COOKIE_NAME, { cookies })

  redirect('/login')
})

export const forgotPassword = action.schema(forgotPasswordSchema).action(async ({ parsedInput: { username } }) => {
  const response = await fetchApi(`${getAppConfig().ssoUrl}/b2c/forgot-password`, {
    method: 'POST',
    body: JSON.stringify({ username }),
  })

  if (response.error) {
    throw createDetailedError(response.error, response.details)
  }

  return { success: true }
})

export const signUp = action.schema(signupSchema).action(async ({ parsedInput }) => {
  const response = await fetchApi(`/profile`, {
    method: 'POST',
    body: JSON.stringify(parsedInput),
  })

  if (response.error) {
    throw createDetailedError(response.error, response.details)
  }

  return { success: true }
})
