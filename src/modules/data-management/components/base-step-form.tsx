import { ReactNode } from 'react'

import { D<PERSON>Form, FormStepperCallbacks, StepConfig } from '@/components/dynamic-form'

import { useDataManagement } from '../hooks/use-data-management'
import {
  createDataManagementFormConfig,
  DataManagementFormOptions,
  prepareStepInitialData,
} from '../libs/form-config-factory'
import { ClientDataManagement } from '../types/data-management-types'

/**
 * Props for the BaseStepForm component
 */
export interface BaseStepFormProps {
  /** The client data management object */
  clientDataManagement: ClientDataManagement
  /** The step configuration */
  step: StepConfig
  /** Existing step data */
  existingData?: any
  /** Custom form configuration options */
  formOptions?: Partial<DataManagementFormOptions>
  /** Custom navigation component */
  navigationComponent?: (props: any) => ReactNode
  /** Component name for debugging */
  componentName: string
  /** Data key for debugging (e.g., 'clientInformation', 'contactData') */
  dataKey: string
}

/**
 * Base form component that encapsulates common logic for all data management step forms
 * This eliminates code duplication across all step form components
 */
export const BaseStepForm = ({
  clientDataManagement,
  step,
  existingData,
  formOptions = {},
  navigationComponent: NavigationComponent,
  componentName,
  dataKey,
}: BaseStepFormProps) => {
  const { handleFormSubmission } = useDataManagement(clientDataManagement)

  // Create form configuration using the factory
  const formConfig = createDataManagementFormConfig({
    step,
    ...formOptions,
  })

  // Form callbacks with standardized logic
  const callbacks: FormStepperCallbacks = {
    onSubmit: async (data) => {
      const stepData = data[step.id]

      // Use the helper function to handle submission automatically
      await handleFormSubmission(stepData)
    },
    // Note: onStepChange is for notifications only, not for navigation logic
    // The actual navigation is handled by the custom DataManagementNavigation component
    onStepChange: ({ currentStepData, direction }) => {
      // This is just a notification callback - no navigation logic here
    },
  }

  // Prepare initial data using the factory function
  const initialData = prepareStepInitialData(step.id, existingData)

  return (
    <div className="space-y-4">
      <DynamicForm
        config={formConfig}
        callbacks={callbacks}
        initialData={initialData}
        navigation={
          NavigationComponent
            ? (props) => <NavigationComponent {...props} clientDataManagement={clientDataManagement} step={step} />
            : undefined
        }
      />
    </div>
  )
}
