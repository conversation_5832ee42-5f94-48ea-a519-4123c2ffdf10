'use client'

import { Card, CardContent } from '@/components/ui/card'
import { DynamicForm, FormConfig, FormStepperCallbacks } from '@/components/dynamic-form'

import dataManagementConfig from '../libs/formConfig.json'

export function DataManagementDynamicForm() {
  const multiStepCallbacks: FormStepperCallbacks = {
    onSubmit: async (data) => {
      console.log('Form submitted:', data)
    },
    onStepChange: async ({ currentStepData, currentStepId, step, direction }) => {
      console.log('Step changed:', currentStepData, currentStepId, step, direction)
    },
  }
  return (
    <Card>
      <CardContent>
        <DynamicForm config={dataManagementConfig as FormConfig} callbacks={multiStepCallbacks} />
      </CardContent>
    </Card>
  )
}
