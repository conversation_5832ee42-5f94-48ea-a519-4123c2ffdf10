'use client'

import { Card, CardContent } from '@/components/ui/card'
import { DynamicForm, FormConfig, FormStepperCallbacks } from '@/components/dynamic-form'

import dataManagementConfig from '../libs/formConfig.json'

export function DataManagementDynamicForm() {
  const multiStepCallbacks: FormStepperCallbacks = {
    onSubmit: async (data) => {
      console.log('Form submitted:', data)
      // Handle final form submission
    },
    onStepChange: async ({ currentStepData, currentStepId, step, direction }) => {
      console.log('Step changed:', currentStepData, currentStepId, step, direction)
      return true
    },
    onArrayItemAdd: async ({ fieldName, stepId, newItem, currentItems }) => {
      console.log('Array item added:', { fieldName, stepId, newItem, currentItems })
      // Validate new credit card data, call APIs if needed
      return true
    },
    onArrayItemRemove: async ({ fieldName, stepId, itemIndex, removedItem, remainingItems }) => {
      console.log('Array item removed:', { fieldName, stepId, itemIndex, removedItem, remainingItems })
      // Handle cleanup, API calls if needed
      return true
    },
    onArrayItemUpdate: async ({ fieldName, stepId, itemIndex, updatedItem, previousItem, allItems }) => {
      console.log('Array item updated:', { fieldName, stepId, itemIndex, updatedItem, previousItem, allItems })
      // Validate updated credit card data
      return true
    },
    onArrayValidation: async ({ fieldName, stepId, items, validationErrors }) => {
      console.log('Array validation:', { fieldName, stepId, items, validationErrors })
      // Custom validation logic for credit card arrays
      return validationErrors.length === 0
    },
  }
  return (
    <Card className="w-full">
      <CardContent>
        <DynamicForm config={dataManagementConfig as FormConfig} callbacks={multiStepCallbacks} />
      </CardContent>
    </Card>
  )
}
