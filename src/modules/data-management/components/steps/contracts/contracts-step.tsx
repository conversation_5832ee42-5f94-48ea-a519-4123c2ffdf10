'use client'

import { useEffect, useMemo, useState } from 'react'

import { createOrUpdateContract } from '@/modules/contracts/api/contracts-actions'
import { ContractForm } from '@/modules/contracts/components/contract-form'
import { Product, Provider } from '@/modules/contracts/libs/contract-types'
import { ContractFormInputs } from '@/modules/contracts/types/contract-schema'
import { useDataManagement } from '@/modules/data-management/hooks/use-data-management'
import { ClientDataManagement, ContractsData } from '@/modules/data-management/types/data-management-types'
import { useTranslations } from 'next-intl'
import { useAction } from 'next-safe-action/hooks'
import { toast } from 'sonner'

import { fileToDocument } from '@/lib/actions/documents/file-to-document'
import { SelectOption } from '@/components/form-inputs'

import { DataManagementNavigation } from '../../data-management-navigation'
import { MiniContractCard } from './mini-contract-card'

interface ContractsStepProps {
  clientDataManagement: ClientDataManagement
  providers: Provider[]
  products: Product[]
  categoryOptions: SelectOption[]
}

export const ContractsStep = ({ clientDataManagement, providers, products, categoryOptions }: ContractsStepProps) => {
  const t = useTranslations()

  const [currentContractIndex, setCurrentContractIndex] = useState(0)
  const [isCreatingNew, setIsCreatingNew] = useState(false)
  const [isSavingContract, setIsSavingContract] = useState(false)

  const { handleFormSubmission, goToPreviousStep, isFirstStep, isSaving, isLastStep, goToNextStep, saveStepDataOnly } =
    useDataManagement(clientDataManagement)

  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  const handleSubmit = async () => {
    await handleFormSubmission(contractsData)
  }

  const {
    executeAsync: updateContract,
    isExecuting,
    isPending,
  } = useAction(createOrUpdateContract, {
    onSuccess: () => {
      // Show success message without redirecting
      toast.success(t('CONTRACTS.EDIT_FORM.EDIT_MODE.TOAST.SUCCESS.MESSAGE'))
      // Stay on current contract after save - don't auto-switch
    },
    onError: () => {
      // Show error message
      toast.error(t('CONTRACTS.EDIT_FORM.EDIT_MODE.TOAST.ERROR.MESSAGE'))
    },
  })
  const contractsData: ContractsData | undefined = clientDataManagement?.steps.contracts
  const contracts = contractsData?.contractsData

  const isSubmitting = isSaving || isExecuting || isPending
  const isContractSaving = isSavingContract || isExecuting || isPending
  const currentContract = isCreatingNew ? null : contractsData?.contractsData?.[currentContractIndex]
  const currentContractId = isCreatingNew ? 'new' : currentContract?.id?.toString() || ''
  const canGoNext = useMemo(() => {
    if (!contracts) return false
    return contracts.every((contract) => contract.isUpdated)
  }, [contracts])

  // Preserve currentContractIndex when contracts data changes and add bounds checking
  useEffect(() => {
    if (contracts && contracts.length > 0) {
      // Ensure currentContractIndex is within bounds
      if (currentContractIndex >= contracts.length) {
        setCurrentContractIndex(contracts.length - 1)
      }
    }
  }, [contracts, currentContractIndex])

  // Tab/box navigation handlers
  const handleTabClick = (idx: number) => {
    setIsCreatingNew(false)
    setCurrentContractIndex(idx)
  }

  // Handle contract update
  const handleContractUpdate = async (contractData: ContractFormInputs) => {
    if (!currentContract?.id) return

    setIsSavingContract(true)
    try {
      // Process files if any
      let files: unknown[] = []
      if (contractData.files) {
        files = await Promise.all(contractData.files.map(async (file) => await fileToDocument(file)))
      }

      // Call the update action using the useAction hook
      const result = await updateContract({
        ...contractData,
        files: files,
        editForm: true,
        contract_id: currentContract.id.toString(),
      })

      if (result?.data) {
        // Update the local contracts data to mark this contract as updated
        if (contractsData?.contractsData) {
          const updatedContracts = contractsData.contractsData.map((contract, index) => {
            if (index === currentContractIndex) {
              return {
                ...contract,
                isUpdated: true,
              }
            }
            return contract
          })

          // Create updated contracts data
          const updatedContractsData: ContractsData = {
            ...contractsData,
            contractsData: updatedContracts,
          }

          // Save the updated contracts data back to the main formData (without navigation)

          console.log('Updated contracts data:', updatedContractsData)
          await saveStepDataOnly('contracts', updatedContractsData)
        }
      }
    } catch (error) {
      console.error('Contract update error:', error)
      // Error handling is done by the useAction hook
    } finally {
      setIsSavingContract(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Contract content */}
      <div className="space-y-6">
        {/* Contract Tabs/Boxes Navigation */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-2 pb-2">
          {contracts?.map((contract, idx) => (
            <MiniContractCard
              key={contract.id}
              contract={contract}
              active={currentContractIndex === idx}
              isUpdated={contract.isUpdated}
              onClick={() => handleTabClick(idx)}
            />
          ))}
        </div>

        {/* Contract Form */}
        {currentContract && (
          <ContractForm
            className="mx-auto max-w-none"
            onComplete={handleContractUpdate}
            editForm={true}
            key={currentContractId} // Force re-render when switching contracts
            contract={currentContract}
            categoryOptions={categoryOptions}
            providers={providers}
            products={products}
            isLoading={isContractSaving} // Pass contract-specific loading state
          />
        )}
      </div>

      {/* Standard data management navigation - only show when all contracts are updated */}
      <DataManagementNavigation
        submitButtonDisabled={!canGoNext}
        nextStepDisabled={!canGoNext}
        previousStepDisabled={isFirstStep}
        isSubmitting={isSubmitting}
        onPrevious={handlePrevious}
        onSubmit={handleSubmit}
        onNext={() => goToNextStep()}
        nextButtonText={t('Next')}
        showSubmitButton={isLastStep}
        clientDataManagement={clientDataManagement}
      />
    </div>
  )
}
