'use client'

import { defaultAddressSchema, type DefaultAddressInputs } from '@/modules/profile/types/profile-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { Card, CardContent, CardForm, CardHeader, CardTitle } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormAddressInput, FormCountrySelect, FormInput } from '@/components/form-inputs'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface DefaultAddressStepProps {
  clientDataManagement: ClientDataManagement
}

export const DefaultAddressStep = ({ clientDataManagement }: DefaultAddressStepProps) => {
  const t = useTranslations()
  const { handleFormSubmission, isSaving, isFirstStep, goToPreviousStep, isLastStep } =
    useDataManagement(clientDataManagement)

  // Get existing default address data if available
  const existingDefaultAddress = clientDataManagement?.steps?.defaultAddress

  const form = useForm<DefaultAddressInputs>({
    mode: 'all',
    shouldFocusError: true,
    resolver: zodResolver(defaultAddressSchema),
    defaultValues: {
      street: existingDefaultAddress?.street || '',
      streetNum: existingDefaultAddress?.streetNum || '',
      zip: existingDefaultAddress?.zip || '',
      city: existingDefaultAddress?.city || '',
      country: existingDefaultAddress?.country || '',
      district: existingDefaultAddress?.district || '',
    },
  })

  const onSubmit = async (data: DefaultAddressInputs) => {
    // Transform data to match the expected format for data management
    const transformedData = {
      ...data,
      lastUpdate: new Date().toISOString(),
    }

    await handleFormSubmission(transformedData)
  }

  // Navigation handlers
  const handleFormValidationAndSubmit = async () => {
    // Trigger form validation and submission (used for both Next and Submit)
    await form.handleSubmit(onSubmit)()
  }

  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  const selectedCountry = form.watch('country')

  return (
    <div className=" mx-auto space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>{t('DATA.MANAGEMENT.STEP.DEFAULT_ADDRESS.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardForm>
                <FormInput<DefaultAddressInputs>
                  name="street"
                  label={t('PROFILE.EDIT_FORM.CONTACT_FIELDSET.STREET_FIELD.LABEL')}
                />

                <FormInput<DefaultAddressInputs>
                  name="streetNum"
                  label={t('PROFILE.EDIT_FORM.CONTACT_FIELDSET.HOUSE_NUMBER_FIELD.LABEL')}
                />

                <FormCountrySelect<DefaultAddressInputs>
                  name="country"
                  label="PROFILE.EDIT_FORM.CONTACT_FIELDSET.COUNTRY_FIELD.LABEL"
                  isRequired
                  onChange={(value) => {
                    form.setValue('country', value)
                    const isGermanAddress = value === 'DE'
                    if (!isGermanAddress) {
                      form.setValue('zip', '')
                      form.setValue('city', '')
                    }
                  }}
                />
                <FormAddressInput<DefaultAddressInputs>
                  selectedCountry={selectedCountry}
                  zipFieldName="zip"
                  cityFieldName="city"
                  zipLabel="PROFILE.EDIT_FORM.CONTACT_FIELDSET.ZIP_FIELD.LABEL"
                  cityLabel="PROFILE.EDIT_FORM.CONTACT_FIELDSET.CITY_FIELD.LABEL"
                  isRequired
                />

                <FormInput<DefaultAddressInputs>
                  name="district"
                  label={t('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.DISTRICT_FIELD.LABEL')}
                />
              </CardForm>
            </CardContent>
          </Card>
        </form>

        <DataManagementNavigation
          submitButtonDisabled={isSaving}
          nextStepDisabled={isSaving}
          previousStepDisabled={isFirstStep}
          clientDataManagement={clientDataManagement}
          isSubmitting={isSaving}
          showSubmitButton={isLastStep}
          onNext={handleFormValidationAndSubmit}
          onPrevious={handlePrevious}
          onSubmit={handleFormValidationAndSubmit}
        />
      </Form>
    </div>
  )
}
