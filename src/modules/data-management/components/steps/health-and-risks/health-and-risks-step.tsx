'use client'

import { useCallback, useMemo } from 'react'

import { RiskForm } from '@/modules/risk/components/risk-form'
import { RiskFormInputs } from '@/modules/risk/types/risk-schema'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface HealthAndRisksStepProps {
  clientDataManagement: ClientDataManagement
}

export const HealthAndRisksStep = ({ clientDataManagement }: HealthAndRisksStepProps) => {
  const { handleFormSubmission, goToPreviousStep, isFirstStep, isSaving, isLastStep } =
    useDataManagement(clientDataManagement)

  // Get existing health and risks data from data management
  const existingHealthAndRisks = clientDataManagement?.steps?.healthAndRisks

  // Use existing data if available, otherwise null
  const initialDataForRiskForm = existingHealthAndRisks || null

  // Check if step has been updated (similar to contracts step)
  const isStepUpdated = useMemo(() => {
    return !!(existingHealthAndRisks && existingHealthAndRisks.lastUpdate)
  }, [existingHealthAndRisks])

  // Determine if next button should be disabled
  const canGoNext = isStepUpdated

  // Handle form validation and submission
  const handleFormValidationAndSubmit = useCallback(
    async (formData: RiskFormInputs) => {
      const healthAndRisksData = {
        ...formData,
        lastUpdate: new Date().toISOString(),
      }

      await handleFormSubmission(healthAndRisksData)
    },
    [handleFormSubmission]
  )

  // Handle previous step
  const handlePrevious = useCallback(async () => {
    await goToPreviousStep()
  }, [goToPreviousStep])

  return (
    <div className="space-y-6">
      {/* Risk Form */}
      <RiskForm
        initialData={initialDataForRiskForm}
        formWrapperClassName="max-w-none"
        isLoading={isSaving}
        customNavigation={({ onSubmit }) => (
          <DataManagementNavigation
            submitButtonDisabled={!canGoNext || isSaving}
            nextStepDisabled={!canGoNext || isSaving}
            previousStepDisabled={isFirstStep}
            clientDataManagement={clientDataManagement}
            isSubmitting={isSaving}
            showSubmitButton={isLastStep}
            onNext={() => onSubmit(handleFormValidationAndSubmit)}
            onPrevious={handlePrevious}
            onSubmit={() => onSubmit(handleFormValidationAndSubmit)}
          />
        )}
      />
    </div>
  )
}
