'use client'

import { useMemo } from 'react'

import { useAction } from 'next-safe-action/hooks'
import { toast } from 'sonner'

import { saveDataManagementAction, saveDataManagementWithProfileValidationAction } from '../api/data-management-actions'
import { hasProfileData, isProfileRelatedStep } from '../libs/profile-mapper'
import {
  ClientDataManagement,
  ClientDataSteps,
  DataManagementStepNames,
  NavigationState,
} from '../types/data-management-types'

export function useDataManagement(formData: ClientDataManagement) {
  const {
    execute: executeSaveData,
    isPending: isSaving,
    result: saveResult,
  } = useAction(saveDataManagementAction, {
    onSuccess: async (data) => {
      // The revalidateTag in the server action should trigger a refresh
      // of the parent component's data
    },
    onError: (error) => {
      console.error('❌ [useDataManagement] Failed to save data:', error)
      toast.error('Failed to save form data. Please try again.')
    },
  })

  // Two-step validation action for profile-related steps
  const {
    execute: executeSaveWithProfileValidation,
    isPending: isSavingWithValidation,
    result: saveWithValidationResult,
  } = useAction(saveDataManagementWithProfileValidationAction, {
    onSuccess: async (data) => {
      // The revalidateTag in the server action should trigger a refresh
      // of the parent component's data
    },
    onError: (error) => {
      console.error('❌ [useDataManagement] Two-step validation failed:', error)

      // Check if it's a profile validation error
      const errorMessage = error.error?.serverError || 'Unknown error'

      if (errorMessage.includes('Profile validation failed')) {
        toast.error('Profile validation failed', {
          description: 'Please check your profile data and try again.',
          duration: 8000,
        })
      } else {
        toast.error('Failed to save form data. Please try again.')
      }
    },
  })

  const currentNavigationState = formData?.navigationState

  /**
   * Save step data without navigation
   * This only updates the form data without changing the current step
   * Profile validation is deferred until final step completion for performance optimization
   */
  const saveStepDataOnly = async (stepName: DataManagementStepNames, stepData: unknown) => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        console.error('❌ [saveStepDataOnly] Navigation steps not available')
        return { success: false, error: 'Navigation state not available' }
      }

      // Prepare step data in the format expected by the API
      // Set stepData directly as the value for the step, adding lastUpdate if not present
      const stepDataWithUpdate = {
        ...(stepData as Record<string, unknown>),
        lastUpdate: new Date().toISOString(),
      }

      const formattedStepData: ClientDataSteps = {
        [stepName]: stepDataWithUpdate,
      }

      // Keep the current navigation state unchanged (no navigation)
      const updatedFormData: ClientDataManagement = {
        steps: {
          ...formData.steps,
          ...formattedStepData,
        },
        navigationState: currentNavigationState, // Keep current navigation state
      }

      executeSaveData({
        formData: updatedFormData,
      })

      return { success: true, stepName }
    } catch (error) {
      console.error('❌ [useDataManagement] Error in saveStepDataOnly:', error)
      return { success: false, error }
    }
  }

  /**
   * Save step data and automatically navigate to next step
   * This preserves the complete form structure and navigation state
   * Profile validation is deferred until final step completion for performance optimization
   */
  const saveStepData = async (stepName: DataManagementStepNames, stepData: unknown) => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        console.error('❌ [saveStepData] Navigation steps not available')
        return { success: false, error: 'Navigation state not available' }
      }

      // Prepare step data in the format expected by the API
      // Set stepData directly as the value for the step, adding lastUpdate if not present
      const stepDataWithUpdate = {
        ...(stepData as Record<string, unknown>),
        lastUpdate: new Date().toISOString(),
      }

      const formattedStepData: ClientDataSteps = {
        [stepName]: stepDataWithUpdate,
      }

      // Update navigation state to mark current step as completed
      const updatedSteps = currentNavigationState.navigationSteps.map((step) => ({
        ...step,
        isCompleted: step.title === stepName ? true : step.isCompleted,
      }))

      // Find next step index
      const currentStepIndex = updatedSteps.findIndex((step) => step.title === stepName)
      const nextStepIndex = currentStepIndex + 1

      // Determine the current step - stay on current step if it's the last one
      const isLastStep = nextStepIndex >= updatedSteps.length
      const nextStepToSet = isLastStep ? stepName : updatedSteps[nextStepIndex].title

      // Update navigation steps with proper active state
      const finalUpdatedSteps = updatedSteps.map((step) => ({
        ...step,
        isActive: step.title === nextStepToSet,
      }))

      const updatedNavigationState: NavigationState = {
        currentStep: nextStepToSet,
        navigationSteps: finalUpdatedSteps,
      }

      const updatedFormData: ClientDataManagement = {
        steps: {
          ...formData.steps,
          ...formattedStepData,
        },
        navigationState: updatedNavigationState,
      }

      // Always use standard save for intermediate steps - profile validation is deferred
      executeSaveData({
        formData: updatedFormData,
      })

      return { success: true, nextStep: nextStepToSet }
    } catch (error) {
      console.error('❌ [useDataManagement] Error in saveStepData:', error)
      return { success: false, error }
    }
  }

  const goToNextStep = async (currentStepData?: Record<string, unknown>) => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        console.error('❌ [goToNextStep] Navigation steps not available')
        return { success: false, error: 'Navigation state not available' }
      }

      const currentStepIndex = currentNavigationState.navigationSteps.findIndex(
        (step) => step.title === currentNavigationState.currentStep
      )

      const nextStepIndex = currentStepIndex + 1

      if (nextStepIndex >= currentNavigationState.navigationSteps.length) {
        return { success: false, error: 'Already at last step' }
      }

      const nextStep = currentNavigationState.navigationSteps[nextStepIndex]

      // Update navigation state for next step
      const updatedSteps = currentNavigationState.navigationSteps.map((step, index) => ({
        ...step,
        isActive: index === nextStepIndex,
        isCompleted: index < nextStepIndex || step.isCompleted,
      }))

      const updatedNavigationState: NavigationState = {
        currentStep: nextStep.title,
        navigationSteps: updatedSteps,
      }

      // Save current step data if provided
      let updatedFormData = formData
      if (currentStepData) {
        // Set stepData directly as the value for the step, adding lastUpdate if not present
        const stepDataWithUpdate = {
          ...(currentStepData as Record<string, unknown>),
          lastUpdate: new Date().toISOString(),
        }

        const formattedStepData: ClientDataSteps = {
          [currentNavigationState.currentStep]: stepDataWithUpdate,
        }

        updatedFormData = {
          steps: {
            ...formData.steps,
            ...formattedStepData,
          },
          navigationState: updatedNavigationState,
        }
      } else {
        updatedFormData = {
          ...formData,
          navigationState: updatedNavigationState,
        }
      }

      executeSaveData({
        formData: updatedFormData,
      })

      return { success: true, nextStep: nextStep.title }
    } catch (error) {
      console.error('❌ [useDataManagement] Error in goToNextStep:', error)
      return { success: false, error }
    }
  }

  const goToPreviousStep = async () => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        console.error('❌ [goToPreviousStep] Navigation steps not available')
        return { success: false, error: 'Navigation state not available' }
      }

      const currentStepIndex = currentNavigationState.navigationSteps.findIndex(
        (step) => step.title === currentNavigationState.currentStep
      )

      const previousStepIndex = currentStepIndex - 1

      if (previousStepIndex < 0) {
        return { success: false, error: 'Already at first step' }
      }

      const previousStep = currentNavigationState.navigationSteps[previousStepIndex]

      // Update navigation state for previous step
      const updatedSteps = currentNavigationState.navigationSteps.map((step, index) => ({
        ...step,
        isActive: index === previousStepIndex,
        // Don't change completion status when going back
      }))

      const updatedNavigationState: NavigationState = {
        currentStep: previousStep.title,
        navigationSteps: updatedSteps,
      }

      const updatedFormData: ClientDataManagement = {
        ...formData,
        navigationState: updatedNavigationState,
      }

      executeSaveData({
        formData: updatedFormData,
      })

      return { success: true, previousStep: previousStep.title }
    } catch (error) {
      console.error('❌ [useDataManagement] Error in goToPreviousStep:', error)
      return { success: false, error }
    }
  }

  const isFirstStep = useMemo(() => {
    if (!currentNavigationState?.navigationSteps) {
      return false
    }

    const currentStepIndex = currentNavigationState.navigationSteps.findIndex(
      (step) => step.title === currentNavigationState.currentStep
    )

    const isFirst = currentStepIndex === 0

    return isFirst
  }, [currentNavigationState])

  const isLastStep = useMemo(() => {
    if (!currentNavigationState?.navigationSteps) return false
    const currentStepIndex = currentNavigationState.navigationSteps.findIndex(
      (step) => step.title === currentNavigationState.currentStep
    )
    return currentStepIndex === currentNavigationState.navigationSteps.length - 1
  }, [currentNavigationState])

  /**
   * Check if all steps are completed
   */
  const isFormComplete = useMemo(() => {
    if (!currentNavigationState?.navigationSteps) return false
    return currentNavigationState.navigationSteps.every((step) => step.isCompleted)
  }, [currentNavigationState])

  const currentStepName = useMemo(() => {
    return currentNavigationState?.currentStep
  }, [currentNavigationState])

  /**
   * Save current step data and navigate to next step
   * This is the main function to call when user clicks "Next"
   */
  const saveAndGoToNextStep = async (currentStepData: unknown) => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        return { success: false, error: 'Navigation state not available' }
      }

      // First save the current step data
      const saveResult = await saveStepData(currentNavigationState.currentStep, currentStepData)

      if (!saveResult.success) {
        return saveResult
      }

      return saveResult
    } catch (error) {
      console.error('❌ [useDataManagement] Error in saveAndGoToNextStep:', error)
      return { success: false, error }
    }
  }

  /**
   * Complete the entire form - save current step and mark as DONE
   * This is called on the last step when user clicks "Complete"
   * Performs profile validation if the form contains profile-related data
   */
  const completeForm = async (currentStepData: unknown) => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        return { success: false, error: 'Navigation state not available' }
      }

      // Prepare step data in the format expected by the API
      // Set stepData directly as the value for the step, adding lastUpdate if not present
      const stepDataWithUpdate = {
        ...(currentStepData as Record<string, unknown>),
        lastUpdate: new Date().toISOString(),
      }

      const formattedStepData: ClientDataSteps = {
        [currentNavigationState.currentStep]: stepDataWithUpdate,
      }

      // Mark all steps as completed
      const completedSteps = currentNavigationState.navigationSteps.map((step) => ({
        ...step,
        isCompleted: true,
      }))

      const updatedNavigationState: NavigationState = {
        currentStep: currentNavigationState.currentStep,
        navigationSteps: completedSteps,
      }

      const updatedFormData: ClientDataManagement = {
        steps: {
          ...formData.steps,
          ...formattedStepData,
        },
        navigationState: updatedNavigationState,
      }

      // Check if the form contains any profile-related data
      const hasProfileDataInForm = hasProfileData(updatedFormData.steps || {})

      // Use profile validation if form contains profile data, otherwise use standard save
      if (hasProfileDataInForm) {
        executeSaveWithProfileValidation({
          formData: updatedFormData,
          status: 'DONE',
        })
      } else {
        executeSaveData({
          formData: updatedFormData,
          status: 'DONE',
        })
      }

      return { success: true, completed: true }
    } catch (error) {
      console.error('❌ [useDataManagement] Error in completeForm:', error)
      return { success: false, error }
    }
  }

  /**
   * Helper function to handle form submission with automatic last step detection
   * This eliminates the need for components to check isLastStep() manually
   */
  const handleFormSubmission = async (currentStepData: unknown) => {
    if (isLastStep) {
      return await completeForm(currentStepData)
    } else {
      return await saveAndGoToNextStep(currentStepData)
    }
  }

  return {
    // Actions
    saveStepData,
    saveStepDataOnly, // New function for saving without navigation
    goToNextStep,
    goToPreviousStep,
    handleFormSubmission, // Helper function that automatically handles last step logic

    // State
    isSaving: isSaving || isSavingWithValidation, // Combined loading state
    saveResult,
    saveWithValidationResult,

    // Utilities
    isLastStep,
    isFirstStep,
    isFormComplete,

    // Current navigation state for components
    currentNavigationState,
    currentStepName,
  }
}
