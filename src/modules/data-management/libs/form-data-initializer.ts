import { ClientDataSteps, DataManagementFormConfig, DataManagementStepNames } from '../types/data-management-types'

export const stepsToRender = (steps: DataManagementFormConfig) =>
  Object.entries(steps)
    .filter(([, value]) => value)
    .map(([key]) => key as DataManagementStepNames)

export function initializeFormData(stepsToRender: DataManagementStepNames[]): ClientDataSteps {
  const allStepTemplates: ClientDataSteps = {
    clientInformation: {
      salutation: null,
      firstName: '',
      lastName: '',
      birthName: '',
      birthdate: undefined,
      familyStatus: null,
      birthPlace: '',
      birthCountry: '',
      nationality: '',
      lastUpdate: '',
    },
    defaultAddress: {
      street: '',
      streetNum: '',
      zip: '',
      city: '',
      country: '',
      district: '',
      lastUpdate: '',
    },
    legitimation: {
      idCardNumber: '',
      idCardDate: undefined,
      idCardAuthority: '',
      lastUpdate: '',
    },
    contactData: {
      email: '',
      preferredContactType: '',
      preferredAppeal: '',
      phone: '',
      mobile: '',
      businessPhone: '',
      lastUpdate: '',
    },
    profession: {
      profession: '',
      jobType: null,
      workPercentagePhysical: undefined,
      incomeYearlyBrutto: '',
      incomeYearlyNetto: '',
      incomeYearlySalaries: '',
      retirementSavingSince: null,
      capitalFormingPayments: '',
      taxOfficeName: '',
      taxNumber: '',
      taxId: '',
      taxClass: null,
      churchTaxPercentage: null,
      healthInsurance: '',
      socialInsuranceNumber: '',
      lastUpdate: '',
    },
    healthAndRisks: {
      weight: undefined,
      height: undefined,
      healthInfo: '',
      additionalRisks: [],
      smoker: undefined,
      lastUpdate: '',
    },
    payment: {
      paymentMethods: [],
      lastUpdate: '',
    },
    contracts: {
      contractsData: [],
      canCreateNew: false,
    },
  }

  // Create result object with only the requested steps
  const result: ClientDataSteps = {}

  stepsToRender.forEach((step) => {
    switch (step) {
      case 'clientInformation':
        result.clientInformation = allStepTemplates.clientInformation
        break
      case 'defaultAddress':
        result.defaultAddress = allStepTemplates.defaultAddress
        break
      case 'legitimation':
        result.legitimation = allStepTemplates.legitimation
        break
      case 'contactData':
        result.contactData = allStepTemplates.contactData
        break
      case 'profession':
        result.profession = allStepTemplates.profession
        break
      case 'healthAndRisks':
        result.healthAndRisks = allStepTemplates.healthAndRisks
        break
      case 'payment':
        result.payment = allStepTemplates.payment
        break
      case 'contracts':
        result.contracts = allStepTemplates.contracts
        break
    }
  })

  return result
}
