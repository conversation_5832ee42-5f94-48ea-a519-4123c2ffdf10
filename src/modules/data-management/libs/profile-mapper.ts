import { ProfileFormInputs } from '@/modules/profile/types/profile-schema'

import { ClientDataSteps } from '../types/data-management-types'

/**
 * Maps data management form data to profile API format
 * Filters out null, undefined, and empty string values to prevent validation errors
 */
export function mapDataManagementToProfile(formData: ClientDataSteps): Partial<ProfileFormInputs> {
  const profileData: Partial<ProfileFormInputs> = {}

  // Helper function to filter out null/undefined/empty values
  const filterValidValues = (obj: Record<string, unknown>): Record<string, unknown> => {
    const filtered: Record<string, unknown> = {}

    for (const [key, value] of Object.entries(obj)) {
      // Only include values that are not null, undefined, or empty strings
      if (value !== null && value !== undefined && value !== '') {
        // For dates, ensure they're properly formatted
        if (value instanceof Date) {
          filtered[key] = value.toISOString()
        } else {
          filtered[key] = value
        }
      }
    }

    return filtered
  }

  // Client information - filter out null values
  if (formData.clientInformation) {
    const filteredClientInfo = filterValidValues(formData.clientInformation)
    Object.assign(profileData, filteredClientInfo)
  }

  // Legitimation - filter out null values
  if (formData.legitimation) {
    const filteredLegitimation = filterValidValues(formData.legitimation)
    Object.assign(profileData, filteredLegitimation)
  }

  // Address - filter out null values
  if (formData.defaultAddress) {
    const filteredAddress = filterValidValues(formData.defaultAddress)
    Object.assign(profileData, filteredAddress)
  }

  // Contact data - filter out null values
  if (formData.contactData) {
    const filteredContactData = filterValidValues(formData.contactData)
    Object.assign(profileData, filteredContactData)
  }

  return profileData
}

/**
 * Extracts profile-related steps from data management form
 */
export function extractProfileSteps(formData: ClientDataSteps): ClientDataSteps {
  const { clientInformation, legitimation, defaultAddress, contactData } = formData
  return { clientInformation, legitimation, defaultAddress, contactData }
}

/**
 * Checks if step is profile-related
 */
export function isProfileRelatedStep(stepName: string): boolean {
  return ['clientInformation', 'legitimation', 'defaultAddress', 'contactData'].includes(stepName)
}

/**
 * Checks if form has any profile data
 */
export function hasProfileData(formData: ClientDataSteps): boolean {
  return !!(formData.clientInformation || formData.legitimation || formData.defaultAddress || formData.contactData)
}

/**
 * Merges existing profile data with form data for profile-related steps
 * This ensures profile-related steps are pre-populated with existing profile data
 */
export function mergeProfileDataWithFormSteps(
  formSteps: ClientDataSteps,
  profileData: Record<string, unknown>
): ClientDataSteps {
  const mergedSteps = { ...formSteps }

  // Helper function to safely set a field value if it doesn't already exist
  const safelySetField = (stepData: Record<string, unknown>, fieldName: string, profileValue: unknown) => {
    // Only set if the field doesn't already have a value and profile value is valid
    if (
      (!stepData[fieldName] || stepData[fieldName] === null || stepData[fieldName] === undefined) &&
      profileValue !== null &&
      profileValue !== undefined &&
      profileValue !== ''
    ) {
      stepData[fieldName] = profileValue
    }
  }

  // Helper function to convert date string to Date object if needed
  const convertDateField = (dateValue: unknown): Date | undefined => {
    if (typeof dateValue === 'string' && dateValue) {
      const date = new Date(dateValue)
      return isNaN(date.getTime()) ? undefined : date
    }
    return undefined
  }

  // Merge Client Information step
  if (!mergedSteps.clientInformation) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mergedSteps.clientInformation = {} as any
  }
  const clientInfo = mergedSteps.clientInformation as Record<string, unknown>

  safelySetField(clientInfo, 'salutation', profileData.salutation)
  safelySetField(clientInfo, 'title', profileData.title)
  safelySetField(clientInfo, 'firstName', profileData.firstName)
  safelySetField(clientInfo, 'lastName', profileData.lastName)
  safelySetField(clientInfo, 'birthName', profileData.birthName)
  safelySetField(clientInfo, 'familyStatus', profileData.familyStatus)
  safelySetField(clientInfo, 'birthPlace', profileData.birthPlace)
  safelySetField(clientInfo, 'birthCountry', profileData.birthCountry)
  safelySetField(clientInfo, 'nationality', profileData.nationality)

  // Handle birthdate conversion from string to Date
  if (profileData.birthdate && !clientInfo.birthdate) {
    const birthDate = convertDateField(profileData.birthdate)
    if (birthDate) {
      clientInfo.birthdate = birthDate
    }
  }

  // Merge Default Address step
  if (!mergedSteps.defaultAddress) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mergedSteps.defaultAddress = {} as any
  }
  const addressInfo = mergedSteps.defaultAddress as Record<string, unknown>

  safelySetField(addressInfo, 'street', profileData.street)
  safelySetField(addressInfo, 'streetNum', profileData.streetNum)
  safelySetField(addressInfo, 'zip', profileData.zip)
  safelySetField(addressInfo, 'city', profileData.city)
  safelySetField(addressInfo, 'country', profileData.country)
  // Note: district is not in UserProfile, so we skip it

  // Merge Contact Data step
  if (!mergedSteps.contactData) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mergedSteps.contactData = {} as any
  }
  const contactInfo = mergedSteps.contactData as Record<string, unknown>

  safelySetField(contactInfo, 'email', profileData.email)
  safelySetField(contactInfo, 'phone', profileData.phone)
  safelySetField(contactInfo, 'mobile', profileData.mobile)
  // Note: businessPhone, preferredContactType, preferredAppeal are not in UserProfile

  // Merge Health and Risks step
  if (!mergedSteps.healthAndRisks) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mergedSteps.healthAndRisks = {} as any
  }
  const healthRisksInfo = mergedSteps.healthAndRisks as Record<string, unknown>

  safelySetField(healthRisksInfo, 'weight', profileData.weight)
  safelySetField(healthRisksInfo, 'height', profileData.height)
  safelySetField(healthRisksInfo, 'healthInfo', profileData.healthInfo)
  safelySetField(healthRisksInfo, 'additionalRisks', profileData.additionalRisks)

  // Transform smoker number to SmokerStatus enum
  if (profileData.smoker !== null && profileData.smoker !== undefined) {
    let smokerStatus: string
    switch (profileData.smoker) {
      case 0:
        smokerStatus = 'NO'
        break
      case 50:
        smokerStatus = 'SOMETIMES'
        break
      case 100:
        smokerStatus = 'YES'
        break
      default:
        smokerStatus = 'UNKNOWN'
    }
    safelySetField(healthRisksInfo, 'smoker', smokerStatus)
  }

  // Legitimation step - these fields are typically not in the basic profile
  // but we'll initialize the step if it doesn't exist
  if (!mergedSteps.legitimation) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mergedSteps.legitimation = {} as any
  }

  return mergedSteps
}
