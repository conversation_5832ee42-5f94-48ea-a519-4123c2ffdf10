import { Contract } from '@/modules/contracts/libs/contract-types'
import { ProfessionFormInputs } from '@/modules/profession/types/profession-schema'
import {
  ClientInformationInputs,
  ContactDataInputs,
  DefaultAddressInputs,
  LegitimationInputs,
} from '@/modules/profile/types/profile-schema'
import { RiskFormInputs } from '@/modules/risk/types/risk-schema'

// Import payment types from payment schema
import type {
  CardType,
  CashData,
  ChequeData,
  CreditCardData,
  PaymentAddress,
  PaymentMethod,
  PaymentMethodType,
  SEPAData,
} from './payment-schema'

/**
 * Backend response structure for client data management
 * This represents the complete response from the backend API
 */
export interface ClientDataManagementBackendResponse {
  id: number
  createdAt: string
  updatedAt: string
  deletedAt: string | null
  invitedAt: string | null
  modifierId: number | null
  modifierType: string | null
  status: string
  agencyId: number
  agentId: number
  clientId: number
  creatorId: number
  formConfiguration: DataManagementFormConfig
  formData: ClientDataSteps | null
  token: string
  visits: number
}

/**
 * Configuration for client data management form
 * Defines which steps are enabled and their settings
 */
export interface DataManagementFormConfig {
  clientInformation: boolean
  legitimation: boolean
  defaultAddress: boolean
  contactData: boolean
  profession: boolean
  healthAndRisks: boolean
  payment: boolean
  contracts?: {
    contractsIds: number[]
    createContracts: boolean
  }
}

/**
 * All possible step names in the data management form
 * These correspond to the form sections that can be enabled/disabled
 */
export type DataManagementStepNames =
  | 'clientInformation'
  | 'legitimation'
  | 'defaultAddress'
  | 'contactData'
  | 'profession'
  | 'healthAndRisks'
  | 'payment'
  | 'contracts'

/**
 * Payment data structure for the payment step
 */
export interface PaymentData {
  paymentMethods: PaymentMethod[]
  lastUpdate?: string
}

// Re-export payment types for convenience
export {
  type CardType,
  type CashData,
  type ChequeData,
  type CreditCardData,
  type PaymentAddress,
  type PaymentMethod,
  type PaymentMethodType,
  type SEPAData,
}

/**
 * Individual step in the form navigation
 */
export interface DataManagementStep {
  title: DataManagementStepNames
  isActive: boolean
  isLast: boolean
  isCompleted: boolean
}

/**
 * Navigation state for form progress tracking
 */
export interface NavigationState {
  currentStep: DataManagementStepNames
  navigationSteps: DataManagementStep[]
}

/**
 * Contract data with update tracking
 */
export interface ContactManagementData extends Contract {
  isUpdated: boolean
}

/**
 * Contracts step data structure
 */
export interface ContractsData {
  contractsData: ContactManagementData[]
  canCreateNew: boolean
}

/**
 * All possible client data steps
 * Each step is optional and includes its own data structure
 * lastUpdate is optional for all steps - will be set by backend when data is saved
 */
export interface ClientDataSteps {
  clientInformation?: ClientInformationInputs & { lastUpdate?: string }
  defaultAddress?: DefaultAddressInputs & { lastUpdate?: string }
  legitimation?: LegitimationInputs & { lastUpdate?: string }
  contactData?: ContactDataInputs & { lastUpdate?: string }
  profession?: ProfessionFormInputs & { lastUpdate?: string }
  healthAndRisks?: RiskFormInputs & { lastUpdate?: string }
  payment?: PaymentData
  contracts?: ContractsData
}

/**
 * Complete client data management structure
 * Contains both the form data and navigation state
 */
export interface ClientDataManagement {
  steps: ClientDataSteps
  navigationState: NavigationState
}
