'use client'

import { useState } from 'react'

import { ChevronDown, ChevronRight, Gift } from 'lucide-react'
import Link from 'next/link'

import { cn } from '@/lib/utils'

import { OfferPackage } from '../libs/offer-types'

interface OfferPackageCardProps {
  package: OfferPackage
  isCollapsible?: boolean
  isExpanded?: boolean
  onToggle?: () => void
  className?: string
}

interface CollapsibleHeaderProps {
  title: string
  isExpanded: boolean
  onToggle: () => void
  className?: string
}

interface PackageCardProps {
  package: OfferPackage
  className?: string
}

// Blue collapsible header component (from Figma design)
function CollapsibleHeader({ title, isExpanded, onToggle, className }: CollapsibleHeaderProps) {
  return (
    <div
      className={cn(
        // Base styling from Figma
        'relative rounded-[10px] shadow-md shrink-0 w-full',
        'bg-[#3966b2] cursor-pointer transition-all duration-200 hover:shadow-lg',
        className
      )}
      onClick={onToggle}
      role="button"
      tabIndex={0}
      aria-expanded={isExpanded}
      aria-controls={`package-content-${title}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          onToggle()
        }
      }}
    >
      <div className="flex flex-col items-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-[131px] items-center justify-start px-6 py-[18px] relative w-full">
          <div className="relative shrink-0 w-full">
            <div className="box-border content-stretch flex flex-row items-center justify-between p-0 relative w-full">
              {/* Title */}
              <div className="relative shrink-0">
                <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative">
                  <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] relative shrink-0 text-[#ffffff] text-[14px] text-left text-nowrap">
                    <p className="block leading-[22px] whitespace-pre">{title}</p>
                  </div>
                </div>
              </div>

              {/* Arrow icon */}
              <div className="relative shrink-0 size-6">
                <div className="absolute contents left-0 top-0">
                  <div className="absolute left-0 size-6 top-0">
                    <ChevronDown
                      className={cn(
                        'block size-full text-white transition-transform duration-200',
                        isExpanded ? 'rotate-180' : 'rotate-0'
                      )}
                      strokeWidth={1.5}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// White package card component (from Figma design)
function PackageCard({ package: pkg, className }: PackageCardProps) {
  // Format date from package (assuming createdAt or similar field)

  return (
    <Link href={`/offers/packages/${pkg.id}`}>
      <div
        className={cn(
          // Base styling from Figma
          'relative rounded-[10px] shadow-md shrink-0 w-full',
          'bg-[#ffffff] cursor-pointer transition-all duration-200 hover:shadow-lg',
          className
        )}
      >
        <div className="flex flex-col items-center relative size-full">
          <div className="box-border content-stretch flex flex-col gap-[131px] items-center justify-start px-6 py-[18px] relative w-full">
            <div className="relative shrink-0 w-full">
              <div className="box-border content-stretch flex flex-row items-center justify-between p-0 relative w-full">
                {/* Left side - Gift icon and package info */}
                <div className="relative shrink-0">
                  <div className="box-border content-stretch flex flex-row gap-4 items-center justify-start p-0 relative">
                    {/* Gift icon */}
                    <div className="relative shrink-0 size-6">
                      <div className="absolute contents left-0 top-0">
                        <div className="absolute left-0 size-6 top-0">
                          <Gift className="block size-full text-[#142a3a]" strokeWidth={1.5} />
                        </div>
                      </div>
                    </div>

                    {/* Package text */}
                    <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] relative shrink-0 text-[#142a3a] text-[14px] text-left text-nowrap">
                      <p className="block leading-[22px] whitespace-pre">{pkg.name}</p>
                    </div>
                  </div>
                </div>

                {/* Right side - Arrow icon */}
                <div className="relative shrink-0 size-6">
                  <div className="absolute contents left-0 top-0">
                    <div className="absolute left-0 size-6 top-0">
                      <ChevronRight className="block size-full text-[#142a3a]" strokeWidth={1.5} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}

// Main component that can be either collapsible or standalone
export function OfferPackageCard({
  package: pkg,
  isCollapsible = true,
  isExpanded = false,
  onToggle,
  className,
}: OfferPackageCardProps) {
  const [internalExpanded, setInternalExpanded] = useState(isExpanded)

  const expanded = onToggle ? isExpanded : internalExpanded
  const handleToggle = onToggle || (() => setInternalExpanded(!internalExpanded))

  if (isCollapsible) {
    return (
      <div className={cn('space-y-2', className)}>
        <CollapsibleHeader title={pkg.module || pkg.name} isExpanded={expanded} onToggle={handleToggle} />
        {expanded && (
          <div id={`package-content-${pkg.module || pkg.name}`} className="space-y-2 pl-4">
            <PackageCard package={pkg} />
          </div>
        )}
      </div>
    )
  }

  return <PackageCard package={pkg} className={className} />
}
