'use client'

import { useCallback, useState } from 'react'

import { updateUserPassword } from '@/modules/settings/api/settings-actions'
import { zodResolver } from '@hookform/resolvers/zod'
import { DialogTitle } from '@radix-ui/react-dialog'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { useTranslations } from 'next-intl'
import { useAction } from 'next-safe-action/hooks'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'
import { FormInput } from '@/components/form-inputs'
import { StatusMessage } from '@/components/status-message'

import { passwordChangeSchema, type PasswordChangeFormInputs } from '../types/settings-schema'

export function SettingsForm() {
  const t = useTranslations()
  const [dialogMode, setConfirmationDialogMode] = useState<'confirm' | 'success' | 'error' | undefined>()
  const closeDialog = useCallback(() => {
    setConfirmationDialogMode(undefined)
  }, [])

  const { executeAsync: updatePassword, isPending: isPasswordUpdatePending } = useAction(updateUserPassword, {
    onError: () => {
      toast.error(t('TOAST_NOTIFICATION.BODY.SAVING_RECORD_FAILED'))
      closeDialog()
    },
    onSuccess: () => {
      setConfirmationDialogMode('success')
      form.reset()
    },
  })

  const form = useForm<PasswordChangeFormInputs>({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      repeatPassword: '',
    },
  })

  const onSubmit = useCallback(async () => {
    setConfirmationDialogMode('confirm')
  }, [])

  const handleConfirmAndSubmit = useCallback(async () => {
    if (!!dialogMode) {
      await updatePassword(form.getValues())
    }
  }, [dialogMode, form, updatePassword])

  const isConfirmMode = dialogMode === 'confirm'

  return (
    <>
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>{t('SETTINGS.CHANGE_PASSWORD.LABEL')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormInput<PasswordChangeFormInputs>
                  name="currentPassword"
                  type="password"
                  label={t('SETTINGS.FORM.OLD_PASSWORD_LABEL')}
                  isRequired
                />
                <FormInput<PasswordChangeFormInputs>
                  name="newPassword"
                  type="password"
                  label={t('SETTINGS.FORM.NEW_PASSWORD_LABEL')}
                  isRequired
                />
                <FormInput<PasswordChangeFormInputs>
                  name="repeatPassword"
                  type="password"
                  label={t('SETTINGS.FORM.REPEAT_NEW_PASSWORD_LABEL')}
                  isRequired
                />

                <div className="pt-4">
                  <Button
                    type="submit"
                    className="w-full bg-slate-800 hover:bg-slate-700 text-white"
                    disabled={isPasswordUpdatePending}
                  >
                    {t(isPasswordUpdatePending ? 'LOADING_PLEASE_WAIT' : 'GLOBAL.SAVE')}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
      <Dialog open={!!dialogMode} onOpenChange={closeDialog}>
        <VisuallyHidden>
          <DialogTitle>
            {t(
              isConfirmMode ? 'SETTINGS.CHANGE_PASSWORD.CONFIRMATION_TITLE' : 'SETTINGS.CHANGE_PASSWORD.SUCCESS_TITLE'
            )}
          </DialogTitle>
        </VisuallyHidden>
        <DialogContent className="max-w-[90vw] lg:max-w-[50vw] ">
          <StatusMessage
            type="edit"
            message={t(
              isConfirmMode
                ? 'SETTINGS.CHANGE_PASSWORD.CONFIRMATION_MESSAGE'
                : 'SETTINGS.CHANGE_PASSWORD.SUCCESS_MESSAGE'
            )}
            title={t(
              isConfirmMode ? 'SETTINGS.CHANGE_PASSWORD.CONFIRMATION_TITLE' : 'SETTINGS.CHANGE_PASSWORD.SUCCESS_TITLE'
            )}
          />
          <DialogFooter className={'flex center sm:justify-center justify-center gap-4'}>
            <Button variant={isConfirmMode ? 'secondary' : 'default'} onClick={closeDialog}>
              {t(isConfirmMode ? 'GLOBAL.CANCEL' : 'GLOBAL.OK')}
            </Button>
            {isConfirmMode && <Button onClick={handleConfirmAndSubmit}>{t('GLOBAL.CONFIRM')}</Button>}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
