'use client'

import { createContext, ReactNode, useContext, useEffect, useState } from 'react'

import { logout } from '@/modules/auth/actions/auth-actions'
import { refreshToken } from '@/modules/auth/actions/session'
import { AuthSession } from '@/modules/auth/types/auth-types'
import { Loader2 } from 'lucide-react'
import { useAction } from 'next-safe-action/hooks'
import { usePathname, useRouter } from 'next/navigation'

import { checkIsSessionExpired } from '@/lib/session-utils'
import { useActivity } from '@/hooks/use-activity'

interface SessionContextType {
  session: AuthSession | null
  isLoading: boolean
}

const SessionContext = createContext<SessionContextType | undefined>(undefined)

interface SessionProviderProps {
  session: AuthSession | null
  children: ReactNode
}

export function SessionProvider({ children, session }: SessionProviderProps) {
  const [sessionState, setSessionState] = useState<AuthSession | null>(() => session || null)
  const [isSessionExpired, setIsSessionExpired] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  const sessionActivity = useActivity({
    enabled: true,
    debug: false,
  })

  const {
    execute: refreshSession,
    isExecuting,
    isPending,
  } = useAction(refreshToken, {
    onSuccess: (res) => {
      if (res.data) {
        setSessionState(res.data as AuthSession)
      }
    },
    onError: async (error) => {
      console.error('refreshToken error', error)
      setSessionState(null)
      await logout()
      router.push('/login')
    },
    onSettled: () => {
      setIsSessionExpired(false)
    },
  })

  useEffect(() => {
    if (checkIsSessionExpired(sessionState)) {
      setIsSessionExpired(true)
    }
  }, [sessionActivity, session, refreshSession, sessionState, pathname])

  useEffect(() => {
    if (isSessionExpired) {
      refreshSession({ refreshToken: sessionState?.refreshToken || '' })
    }
  }, [isSessionExpired, refreshSession, sessionState])

  const isRefreshing = isExecuting || isPending

  return (
    <SessionContext.Provider
      value={{
        session: sessionState,
        isLoading: isExecuting || isPending,
      }}
    >
      {isRefreshing && sessionState ? (
        <div className="flex justify-center items-center h-screen w-screen">
          <div className="text-center">
            <Loader2 className="animate-spin h-8 w-8 text-primary mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Refreshing session...</p>
          </div>
        </div>
      ) : (
        children
      )}
    </SessionContext.Provider>
  )
}

// Hook to use session context
export function useSession() {
  const context = useContext(SessionContext)
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider')
  }
  return context
}
